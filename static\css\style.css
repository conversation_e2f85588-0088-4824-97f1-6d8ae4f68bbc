/* Modern CSS file for Agriculture Hub */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
    /* Primary Color Palette - Rich Green */
    --primary-color: #2c7a40;
    --primary-light: #4caf50;
    --primary-dark: #1b5e20;
    --primary-gradient: linear-gradient(135deg, #4caf50, #1b5e20);
    --primary-gradient-soft: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(27, 94, 32, 0.1));

    /* Secondary Color Palette - Golden Accent */
    --secondary-color: #ffc107;
    --secondary-light: #ffd54f;
    --secondary-dark: #ff8f00;
    --secondary-gradient: linear-gradient(135deg, #ffc107, #ff8f00);
    --secondary-gradient-soft: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 143, 0, 0.1));

    /* Tertiary Colors - Earth Tones */
    --tertiary-color: #8d6e63;
    --tertiary-light: #a1887f;
    --tertiary-dark: #5d4037;
    --tertiary-gradient: linear-gradient(135deg, #8d6e63, #5d4037);

    /* Neutral Colors */
    --text-on-primary: #ffffff;
    --text-on-dark: #f5f5f5;
    --text-primary: #263238;
    --text-secondary: #546e7a;
    --text-muted: #78909c;
    --background-light: #f8f9fa;
    --background-white: #ffffff;
    --background-accent: #e8f5e9;
    --background-accent-secondary: #fff8e1;
    --background-dark: #263238;

    /* UI Elements */
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --hover-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 16px;
    --border-radius-sm: 8px;
    --border-radius-lg: 24px;
    --border-color: rgba(0, 0, 0, 0.08);
    --divider-color: rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Typography */
    --font-primary: 'Montserrat', sans-serif;
    --font-secondary: 'Poppins', sans-serif;
    --font-accent: 'Playfair Display', serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;
    --font-size-xxxl: 3rem;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: var(--font-secondary);
    background-color: var(--background-light);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    letter-spacing: 0.01em;
}

main {
    padding-bottom: 2rem;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 {
    font-size: var(--font-size-xxxl);
    font-weight: 700;
    letter-spacing: -0.02em;
}

h2 {
    font-size: var(--font-size-xxl);
    letter-spacing: -0.01em;
}

h3 {
    font-size: var(--font-size-xl);
}

h4 {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

.accent-heading {
    font-family: var(--font-accent);
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: 400;
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-normal);
    font-weight: 500;
}

a:hover {
    color: var(--primary-dark);
}

.section-padding {
    padding: var(--spacing-xxl) 0;
}

.section-divider {
    height: 1px;
    background: var(--divider-color);
    margin: var(--spacing-xl) 0;
    position: relative;
}

.section-divider::after {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-tertiary {
    color: var(--tertiary-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.bg-primary-gradient {
    background: var(--primary-gradient);
}

.bg-secondary-gradient {
    background: var(--secondary-gradient);
}

.bg-tertiary-gradient {
    background: var(--tertiary-gradient);
}

.bg-primary-soft {
    background: var(--primary-gradient-soft);
}

.bg-secondary-soft {
    background: var(--secondary-gradient-soft);
}

.bg-accent {
    background-color: var(--background-accent);
}

.bg-accent-secondary {
    background-color: var(--background-accent-secondary);
}

.footer {
    margin-top: auto;
    padding: var(--spacing-lg) 0;
    background-color: var(--background-dark);
    color: var(--text-on-dark);
    position: relative;
    flex-shrink: 0;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.footer h5 {
    color: var(--text-on-dark);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

.footer p, .footer a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer ul.list-unstyled li {
    margin-bottom: 0.25rem;
}

.footer a:hover {
    color: var(--secondary-light);
}

/* Header and Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 0.5px;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 4px;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 10rem 0 8rem;
    margin-bottom: var(--spacing-xxl);
    position: relative;
    overflow: hidden;
    text-align: center;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(135deg, rgba(27, 94, 32, 0.4), rgba(76, 175, 80, 0.1));
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 80px;
    background-color: var(--background-light);
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 60%, 0 0);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
}

.hero-section h1 {
    font-size: var(--font-size-xxxl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
    color: var(--text-on-primary);
    position: relative;
    display: inline-block;
}

.hero-section h1::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--secondary-gradient);
    border-radius: 2px;
}

.hero-section p {
    font-size: var(--font-size-lg);
    max-width: 800px;
    margin: 2rem auto var(--spacing-xl);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    opacity: 0.95;
    color: var(--text-on-primary);
    font-weight: 300;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1.25rem;
    border-radius: 50px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: var(--spacing-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-on-primary);
}

/* Cards */
.card {
    box-shadow: var(--card-shadow);
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-xl);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
    background-color: var(--background-white);
    height: 100%;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.card-header {
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    font-weight: 600;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: none;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--secondary-gradient);
    opacity: 0.7;
}

.card-body {
    padding: var(--spacing-xl);
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.card-text {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* Buttons */
.btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: var(--font-size-sm);
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.15);
    transition: var(--transition-normal);
    z-index: -1;
}

.btn:hover::before {
    width: 100%;
}

.btn-success {
    background: var(--primary-gradient);
    border: none;
    color: var(--text-on-primary);
    box-shadow: 0 4px 15px rgba(27, 94, 32, 0.25);
}

.btn-success:hover, .btn-success:focus {
    background: var(--primary-gradient);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(27, 94, 32, 0.35);
    color: var(--text-on-primary);
}

.btn-secondary {
    background: var(--secondary-gradient);
    border: none;
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(255, 143, 0, 0.25);
}

.btn-secondary:hover, .btn-secondary:focus {
    background: var(--secondary-gradient);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 143, 0, 0.35);
    color: var(--text-primary);
}

.btn-tertiary {
    background: var(--tertiary-gradient);
    border: none;
    color: var(--text-on-primary);
    box-shadow: 0 4px 15px rgba(93, 64, 55, 0.25);
}

.btn-tertiary:hover, .btn-tertiary:focus {
    background: var(--tertiary-gradient);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(93, 64, 55, 0.35);
    color: var(--text-on-primary);
}

.btn-outline-success {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-success:hover, .btn-outline-success:focus {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(27, 94, 32, 0.25);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus {
    background-color: var(--secondary-color);
    color: var(--text-primary);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 143, 0, 0.25);
}

.btn-outline-light {
    color: var(--text-on-primary);
    border: 2px solid rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
}

.btn-outline-light:hover, .btn-outline-light:focus {
    background-color: var(--text-on-primary);
    color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: var(--text-on-primary);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: var(--font-size-md);
}

.btn-sm {
    padding: 0.5rem 1.5rem;
    font-size: var(--font-size-xs);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-icon i {
    font-size: 1.2em;
    transition: var(--transition-normal);
}

.btn:hover .btn-icon i,
.btn:focus .btn-icon i {
    transform: translateX(3px);
}

/* Form elements */
.form-control {
    border-radius: var(--border-radius-sm);
    padding: 0.85rem 1.25rem;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
    font-size: var(--font-size-md);
    background-color: var(--background-white);
    color: var(--text-primary);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(58, 143, 77, 0.15);
    outline: none;
}

.form-control::placeholder {
    color: #adb5bd;
    opacity: 0.7;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    letter-spacing: 0.5px;
}

.form-text {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    margin-top: 0.5rem;
}

.form-select {
    border-radius: var(--border-radius-sm);
    padding: 0.85rem 1.25rem;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
    font-size: var(--font-size-md);
    background-color: var(--background-white);
    color: var(--text-primary);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(58, 143, 77, 0.15);
    outline: none;
}

.form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.15em;
    background-color: var(--background-white);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(58, 143, 77, 0.15);
    outline: none;
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-left: 0.5rem;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: #f1f8e9;
    font-weight: 600;
    border-top: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Dashboard specific */
.dashboard-card {
    height: 100%;
}

.chart-container {
    width: 100%;
    max-height: 400px;
    padding: 1rem;
    background-color: white;
    border-radius: var(--border-radius);
}

/* Health indicators */
.health-good {
    color: var(--primary-color);
    font-weight: 600;
}

.health-warning {
    color: var(--secondary-color);
    font-weight: 600;
}

.health-danger {
    color: #dc3545;
    font-weight: 600;
}

/* Feature cards on homepage */
.feature-card {
    text-align: center;
    height: 100%;
    padding: var(--spacing-xl);
    background-color: var(--background-white);
    transition: var(--transition-normal);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-gradient);
    transition: var(--transition-normal);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.feature-card:hover::before {
    height: 100%;
    opacity: 0.05;
}

.feature-card-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient-soft);
    border-radius: 50%;
    transition: var(--transition-normal);
    border: 1px solid rgba(76, 175, 80, 0.1);
}

.feature-card:hover .feature-card-icon {
    background: var(--primary-gradient);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 20px rgba(27, 94, 32, 0.2);
    border-color: transparent;
}

.feature-card:hover .feature-card-icon i,
.feature-card:hover .feature-card-icon img {
    color: var(--text-on-primary);
    filter: brightness(1.1);
}

.feature-card-icon i {
    font-size: 2.5rem;
    color: var(--primary-color);
    transition: var(--transition-normal);
}

.feature-card img {
    max-width: 70px;
    height: 70px;
    margin: 0 auto;
    transition: var(--transition-normal);
    object-fit: contain;
}

.feature-card:hover img {
    transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    font-size: var(--font-size-lg);
    position: relative;
    display: inline-block;
}

.feature-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    transition: var(--transition-normal);
    border-radius: 3px;
}

.feature-card:hover h3::after {
    width: 80px;
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-size-sm);
    line-height: 1.7;
}

.feature-card .btn {
    margin-top: auto;
}

/* Alternate feature card styles */
.feature-card-horizontal {
    display: flex;
    text-align: left;
    padding: var(--spacing-lg);
    align-items: center;
}

.feature-card-horizontal .feature-card-icon {
    margin: 0 var(--spacing-lg) 0 0;
    flex-shrink: 0;
}

.feature-card-horizontal h3 {
    display: block;
}

.feature-card-horizontal h3::after {
    left: 0;
    transform: none;
}

.feature-card-horizontal:hover h3::after {
    width: 60px;
}

.feature-card-minimal {
    background: transparent;
    box-shadow: none;
    border: none;
    padding: var(--spacing-lg) 0;
}

.feature-card-minimal::before {
    display: none;
}

.feature-card-minimal:hover {
    transform: translateY(-5px);
    box-shadow: none;
}

.feature-card-minimal .feature-card-icon {
    background: var(--background-accent);
    width: 70px;
    height: 70px;
}

.feature-card-minimal:hover .feature-card-icon {
    background: var(--primary-gradient);
    transform: scale(1.1);
    box-shadow: 0 8px 15px rgba(27, 94, 32, 0.15);
}

.feature-card-minimal h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-sm);
}

.feature-card-minimal h3::after {
    display: none;
}

.feature-card-minimal p {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-md);
}

/* Chatbot styles */
.chat-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 20px;
    height: 500px;
    display: flex;
    flex-direction: column;
}

#chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 80%;
    padding: 12px 18px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.user .message-content {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.message.bot .message-content {
    background-color: #f5f5f5;
    color: #333;
}

.input-area {
    display: flex;
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
}

#user-input {
    flex-grow: 1;
    padding: 12px 20px;
    border: 1px solid #ddd;
    border-radius: 30px;
    margin-right: 10px;
    font-size: 1rem;
}

#send-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 12px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#send-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Plant disease detection */
.upload-box {
    border: 2px dashed var(--primary-color);
    padding: 2.5rem;
    text-align: center;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    background-color: #f1f8e9;
    transition: all 0.3s ease;
}

.upload-box:hover {
    background-color: #e8f5e9;
}

.result {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 20px;
    box-shadow: var(--card-shadow);
}

.healthy {
    background-color: #e8f5e9;
    border-left: 5px solid #4caf50;
}

.diseased {
    background-color: #ffebee;
    border-left: 5px solid #f44336;
}

.solution {
    background-color: #e3f2fd;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes zoomIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-zoom-in {
    animation: zoomIn 0.8s ease-out forwards;
}

.animate-bounce {
    animation: bounce 2s ease infinite;
}

.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

/* Utility Classes */
.shadow-sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.shadow-md {
    box-shadow: var(--card-shadow);
}

.shadow-lg {
    box-shadow: var(--hover-shadow);
}

.rounded-sm {
    border-radius: var(--border-radius-sm);
}

.rounded-md {
    border-radius: var(--border-radius);
}

.rounded-lg {
    border-radius: var(--border-radius-lg);
}

.bg-gradient-primary {
    background: var(--primary-gradient);
}

.bg-gradient-secondary {
    background: var(--secondary-gradient);
}

.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.text-gradient-secondary {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    :root {
        --font-size-xxxl: 2.5rem;
        --font-size-xxl: 1.8rem;
        --font-size-xl: 1.4rem;
    }

    .hero-section {
        padding: 6rem 0;
    }

    .hero-section::after {
        height: 60px;
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    .feature-card-icon {
        width: 70px;
        height: 70px;
    }

    .feature-card img {
        max-width: 80px;
        height: 80px;
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-xxxl: 2rem;
        --font-size-xxl: 1.6rem;
        --font-size-xl: 1.3rem;
        --spacing-xxl: 2rem;
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .hero-section {
        padding: 4rem 0;
    }

    .hero-section::after {
        height: 40px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .section-padding {
        padding: var(--spacing-xl) 0;
    }
}

@media (max-width: 576px) {
    :root {
        --font-size-xxxl: 1.8rem;
        --font-size-xxl: 1.5rem;
        --font-size-xl: 1.2rem;
        --spacing-xl: 1.5rem;
        --spacing-lg: 1rem;
    }

    .btn {
        padding: 0.6rem 1.5rem;
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    .feature-card-icon {
        width: 60px;
        height: 60px;
    }

    .feature-card img {
        max-width: 70px;
        height: 70px;
    }

    .form-control, .form-select {
        padding: 0.75rem 1rem;
    }
}
