/* Modern Navigation Bar Styles for Agriculture Hub */

:root {
    /* Navbar Colors - Improved color scheme */
    --navbar-bg: #ffffff;
    --navbar-text: #2e3d42;
    --navbar-active: #2c7a40;
    --navbar-hover: #4caf50;
    --navbar-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    --navbar-border-radius: 6px;
    --navbar-transition: all 0.3s ease;
    --navbar-logo-color: #2c7a40;

    /* Dropdown Menu */
    --navbar-dropdown-bg: #ffffff;
    --navbar-dropdown-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --navbar-dropdown-border: 1px solid rgba(0, 0, 0, 0.05);
    --navbar-dropdown-hover: #e8f5e9;

    /* Mobile Navigation */
    --navbar-mobile-bg: #ffffff;
    --navbar-mobile-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --navbar-mobile-border: 1px solid rgba(0, 0, 0, 0.05);
    --navbar-mobile-active: #e8f5e9;

    /* Gradients */
    --navbar-active-gradient: linear-gradient(90deg, #4caf50, #1b5e20);
    --navbar-hover-gradient: linear-gradient(90deg, #4caf50, #2c7a40);
    --navbar-active-gradient-soft: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(27, 94, 32, 0.1));
}

/* Main Navbar Styles - Improved design */
.modern-navbar {
    background-color: var(--navbar-bg);
    box-shadow: var(--navbar-shadow);
    padding: 0.6rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--navbar-transition);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-navbar.scrolled {
    padding: 0.5rem 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
}

.modern-navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modern-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.3rem;
    color: var(--navbar-active);
    display: flex;
    align-items: center;
    transition: var(--navbar-transition);
    letter-spacing: 0.5px;
    margin-right: 1.5rem;
    padding: 0.2rem 0.5rem;
    border-radius: var(--navbar-border-radius);
}

.modern-navbar .navbar-brand:hover {
    transform: translateY(-1px);
    color: var(--navbar-hover);
    background-color: rgba(76, 175, 80, 0.05);
}

.modern-navbar .navbar-brand i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
    color: var(--navbar-active);
}

.modern-navbar .navbar-toggler {
    border: none;
    padding: 0.4rem;
    color: var(--navbar-text);
    transition: var(--navbar-transition);
    background-color: rgba(58, 143, 77, 0.1);
    border-radius: 6px;
}

.modern-navbar .navbar-toggler:hover {
    background-color: rgba(58, 143, 77, 0.15);
}

.modern-navbar .navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(58, 143, 77, 0.2);
    outline: none;
}

.modern-navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(58, 143, 77, 1)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Navbar Links */
.modern-navbar .navbar-nav {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

.modern-navbar .navbar-nav .nav-item {
    margin: 0 0.2rem;
    position: relative;
    list-style: none;
}

.modern-navbar .navbar-nav .nav-link {
    color: var(--navbar-text);
    font-weight: 500;
    padding: 0.5rem 0.8rem;
    border-radius: var(--navbar-border-radius);
    transition: var(--navbar-transition);
    position: relative;
    font-size: 0.9rem;
    letter-spacing: 0.2px;
    overflow: hidden;
    z-index: 1;
    display: flex;
    align-items: center;
    margin: 0 0.1rem;
}

.modern-navbar .navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--navbar-hover-gradient);
    opacity: 0.08;
    transition: var(--navbar-transition);
    z-index: -1;
}

.modern-navbar .navbar-nav .nav-link:hover {
    color: var(--navbar-active);
    transform: translateY(-1px);
}

.modern-navbar .navbar-nav .nav-link:hover::before {
    height: 100%;
}

.modern-navbar .navbar-nav .nav-link.active {
    color: var(--navbar-active);
    font-weight: 600;
    background-color: rgba(76, 175, 80, 0.1);
    box-shadow: 0 2px 5px rgba(76, 175, 80, 0.15);
}

.modern-navbar .navbar-nav .nav-link.active::before {
    height: 100%;
    opacity: 0.15;
}

.modern-navbar .navbar-nav .nav-link i {
    margin-right: 0.4rem;
    font-size: 1rem;
    transition: var(--navbar-transition);
}

.modern-navbar .navbar-nav .nav-link:hover i {
    transform: translateY(-1px);
}

/* Animated Underline for Active Link */
.modern-navbar .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0.3rem;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--navbar-active-gradient);
    transition: var(--navbar-transition);
    transform: translateX(-50%);
    opacity: 0;
    border-radius: 2px;
}

.modern-navbar .navbar-nav .nav-link.active::after {
    width: 30px;
    opacity: 1;
}

.modern-navbar .navbar-nav .nav-link:hover::after {
    width: 20px;
    opacity: 0.7;
}

/* Dashboard Button */
.modern-navbar .dashboard-btn {
    background: var(--navbar-active-gradient);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.75rem;
    font-weight: 600;
    transition: var(--navbar-transition);
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(58, 143, 77, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
}

.modern-navbar .dashboard-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--navbar-transition);
    z-index: -1;
}

.modern-navbar .dashboard-btn:hover::before {
    width: 100%;
}

.modern-navbar .dashboard-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(58, 143, 77, 0.4);
}

.modern-navbar .dashboard-btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    transition: var(--navbar-transition);
}

.modern-navbar .dashboard-btn:hover i {
    transform: translateX(3px);
}

/* Dropdown Menu */
.modern-navbar .dropdown-menu {
    border: var(--navbar-dropdown-border);
    border-radius: var(--navbar-border-radius);
    box-shadow: var(--navbar-dropdown-shadow);
    padding: 0.75rem;
    min-width: 220px;
    margin-top: 0.75rem;
    background-color: var(--navbar-dropdown-bg);
    animation: fadeInDown 0.3s ease;
    overflow: hidden;
    position: relative;
}

.modern-navbar .dropdown-menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--navbar-active-gradient);
}

.modern-navbar .dropdown-item {
    padding: 0.85rem 1.25rem;
    font-weight: 500;
    color: var(--navbar-text);
    transition: var(--navbar-transition);
    border-radius: 10px;
    margin-bottom: 0.25rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.modern-navbar .dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--navbar-hover-gradient);
    opacity: 0.1;
    transition: var(--navbar-transition);
    z-index: -1;
}

.modern-navbar .dropdown-item:hover::before {
    width: 100%;
}

.modern-navbar .dropdown-item:hover {
    background-color: transparent;
    color: var(--navbar-active);
    transform: translateX(5px);
}

.modern-navbar .dropdown-item i {
    margin-right: 0.75rem;
    color: var(--navbar-active);
    font-size: 1rem;
    transition: var(--navbar-transition);
}

.modern-navbar .dropdown-item:hover i {
    transform: translateX(3px);
}

/* Mobile Navigation */
@media (max-width: 991.98px) {
    .modern-navbar .container {
        width: 100%;
        padding: 0 1rem;
    }

    .modern-navbar .navbar-collapse {
        background-color: var(--navbar-mobile-bg);
        border-radius: var(--navbar-border-radius);
        box-shadow: var(--navbar-mobile-shadow);
        border: var(--navbar-mobile-border);
        padding: 0.8rem;
        margin-top: 0.8rem;
        max-height: 80vh;
        overflow-y: auto;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        border-top: 3px solid var(--navbar-active);
    }

    .modern-navbar .navbar-collapse::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--navbar-active-gradient);
    }

    .modern-navbar .navbar-nav {
        flex-direction: column;
        width: 100%;
        padding: 0.5rem 0;
    }

    .modern-navbar .navbar-nav .nav-item {
        margin: 0.25rem 0;
        width: 100%;
    }

    .modern-navbar .navbar-nav .nav-link {
        padding: 0.7rem 1rem;
        border-radius: var(--navbar-border-radius);
        width: 100%;
        justify-content: flex-start;
    }

    .modern-navbar .navbar-nav .nav-link.active {
        background-color: var(--navbar-mobile-active);
    }

    .modern-navbar .navbar-nav .nav-link::after {
        display: none;
    }

    .modern-navbar .navbar-nav .nav-link i {
        width: 24px;
        text-align: center;
        margin-right: 0.75rem;
    }

    .modern-navbar .navbar-brand {
        margin-right: 0;
    }

    .modern-navbar .dropdown-menu {
        border: none;
        box-shadow: none;
        padding: 0.5rem;
        margin: 0.25rem 0;
        background-color: rgba(58, 143, 77, 0.05);
        border-radius: 8px;
        position: static !important;
        transform: none !important;
    }

    .modern-navbar .dropdown-menu::before {
        display: none;
    }

    .modern-navbar .dropdown-item {
        padding: 0.6rem 1rem;
        margin-bottom: 0.1rem;
    }

    .modern-navbar .dropdown-item:hover {
        transform: translateX(3px);
    }

    /* Hide notification on mobile */
    .modern-navbar .notification-badge {
        display: none !important;
    }
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Navbar additional styles can be added here if needed */

/* Notification Badge */
.notification-badge {
    position: relative;
    margin-right: 0.5rem;
}

.notification-badge .badge {
    position: absolute;
    top: -3px;
    right: -3px;
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 0.65rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
}

.notification-badge .btn {
    background-color: transparent;
    border: none;
    color: var(--navbar-text);
    font-size: 1rem;
    padding: 0.4rem;
    border-radius: 50%;
    transition: var(--navbar-transition);
}

.notification-badge .btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--navbar-active);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
}

.user-profile .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.5rem;
    border: 2px solid var(--navbar-active);
}

.user-profile .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-profile .user-info {
    display: none;
}

@media (min-width: 1200px) {
    .user-profile .user-info {
        display: block;
    }

    .user-profile .user-name {
        font-weight: 600;
        font-size: 0.9rem;
        margin: 0;
        line-height: 1.2;
    }

    .user-profile .user-role {
        font-size: 0.75rem;
        color: var(--navbar-text);
        opacity: 0.7;
        margin: 0;
    }
}
