{% extends 'base_new.html' %}

{% block title %}Plant Disease Detection - Agriculture Hub{% endblock %}

{% block extra_css %}
<style>
    .upload-box {
        border: 2px dashed #2e7d32;
        padding: 2.5rem;
        text-align: center;
        margin-bottom: 20px;
        border-radius: 12px;
        background-color: #f1f8e9;
        transition: all 0.3s ease;
    }

    .upload-box:hover {
        background-color: #e8f5e9;
    }

    .result {
        padding: 1.5rem;
        border-radius: 12px;
        margin-top: 20px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .healthy {
        background-color: #e8f5e9;
        border-left: 5px solid #4caf50;
    }

    .diseased {
        background-color: #ffebee;
        border-left: 5px solid #f44336;
    }

    .solution {
        background-color: #e3f2fd;
        padding: 1.5rem;
        border-radius: 12px;
        margin-top: 1.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .disease-image {
        max-height: 300px;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.3s linear;
    }

    .loading-spinner {
        width: 80px;
        height: 80px;
        border: 8px solid #f3f3f3;
        border-top: 8px solid #2e7d32;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 text-center">
        <h1 class="mb-4">Plant Disease Detection</h1>
        <p class="lead">Upload a photo of a plant leaf to identify diseases and get treatment recommendations</p>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3 class="mb-0">Upload Plant Image</h3>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger">
                    <h5>⚠️ Error</h5>
                    <p>{{ error }}</p>
                </div>
                {% endif %}

                <form id="disease-form" method="post" action="{{ url_for('plant_disease_predict') }}" enctype="multipart/form-data" onsubmit="showLoading()">
                    <div class="upload-box">
                        <p>Upload a clear photo of a plant leaf:</p>
                        <input type="file" name="file" id="image-upload" accept="image/*" required class="form-control">
                        <small class="text-muted d-block mt-2">For best results, use a well-lit, close-up image of the leaf showing any visible symptoms.</small>
                        <div class="mt-3" id="image-preview-container" style="display: none;">
                            <img id="image-preview" class="img-fluid rounded" style="max-height: 200px;" alt="Image preview">
                        </div>
                    </div>
                    <div class="d-grid gap-2 mt-3">
                        <button type="submit" class="btn btn-success btn-lg">Diagnose Plant</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if prediction %}
<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card result {% if is_healthy %}healthy{% else %}diseased{% endif %}">
            <div class="card-header {% if is_healthy %}bg-success{% else %}bg-danger{% endif %} text-white">
                <h3 class="mb-0">
                    {% if is_healthy %}
                    ✅ Healthy Plant
                    {% else %}
                    🚨 {{ prediction }}
                    {% endif %}
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Confidence:</strong> {{ confidence }}</p>

                        {% if image_path %}
                        <img src="{{ image_path }}" alt="Plant leaf" class="img-fluid rounded mb-3 disease-image">
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="solution">
                            <h4>Recommended Solution:</h4>
                            <p>{{ solution }}</p>

                            <h4>Prevention Tips:</h4>
                            <ul>
                                {% for tip in tips %}
                                <li>{{ tip }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3 class="mb-0">How It Works</h3>
            </div>
            <div class="card-body">
                <p>Our plant disease detection system uses computer vision and deep learning to identify diseases in plant leaves from images.</p>
                <ol>
                    <li>Upload a clear image of a plant leaf showing symptoms</li>
                    <li>Our AI model analyzes the image to identify patterns associated with various plant diseases</li>
                    <li>Receive a diagnosis with confidence level</li>
                    <li>Get recommended treatments and prevention tips specific to the identified disease</li>
                </ol>
                <p>Early detection of plant diseases can help prevent crop losses and reduce the need for extensive chemical treatments.</p>

                <div class="alert alert-info mt-3">
                    <p class="mb-0"><strong>Note:</strong> While our system is highly accurate, it should be used as a preliminary diagnostic tool. For severe or persistent issues, consult with a professional plant pathologist or agricultural extension service.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div id="loading-overlay" class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Show image preview when a file is selected
    document.getElementById('image-upload').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewContainer = document.getElementById('image-preview-container');
                const preview = document.getElementById('image-preview');
                preview.src = e.target.result;
                previewContainer.style.display = 'block';
            }
            reader.readAsDataURL(file);
        }
    });

    // Show loading overlay when form is submitted
    function showLoading() {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.visibility = 'visible';
        overlay.style.opacity = '1';
        return true;
    }
</script>
{% endblock %}
